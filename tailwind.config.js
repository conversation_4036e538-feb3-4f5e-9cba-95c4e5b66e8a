/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      animation: {
        blob: "blob 7s infinite",
        slash: "slash 3s ease-in-out infinite",
        "price-drop": "price-drop 2s ease-out infinite",
        "savings-pulse": "savings-pulse 2s ease-in-out infinite",
      },
      keyframes: {
        blob: {
          "0%": {
            transform: "translate(0px, 0px) scale(1)",
          },
          "33%": {
            transform: "translate(30px, -50px) scale(1.1)",
          },
          "66%": {
            transform: "translate(-20px, 20px) scale(0.9)",
          },
          "100%": {
            transform: "translate(0px, 0px) scale(1)",
          },
        },
        slash: {
          "0%": {
            transform: "scale(0) rotate(-45deg)",
            opacity: "0",
          },
          "25%": {
            transform: "scale(1.3) rotate(0deg)",
            opacity: "1",
            filter: "drop-shadow(0 0 20px rgba(255, 193, 7, 0.8))",
          },
          "50%": {
            transform: "scale(1.1) rotate(15deg)",
            opacity: "0.9",
          },
          "75%": {
            transform: "scale(1.2) rotate(-10deg)",
            opacity: "0.8",
          },
          "100%": {
            transform: "scale(1) rotate(0deg)",
            opacity: "0.7",
          },
        },
        "price-drop": {
          "0%": {
            transform: "translateY(-30px) scale(0.5) rotate(-10deg)",
            opacity: "0",
            filter: "blur(2px)",
          },
          "30%": {
            transform: "translateY(-15px) scale(0.8) rotate(5deg)",
            opacity: "0.5",
            filter: "blur(1px)",
          },
          "60%": {
            transform: "translateY(-5px) scale(1.1) rotate(-2deg)",
            opacity: "0.8",
            filter: "blur(0px)",
          },
          "80%": {
            transform: "translateY(2px) scale(1.05) rotate(1deg)",
            opacity: "0.9",
          },
          "100%": {
            transform: "translateY(0) scale(1) rotate(0deg)",
            opacity: "1",
            filter: "drop-shadow(0 0 15px rgba(34, 197, 94, 0.6))",
          },
        },
        "savings-pulse": {
          "0%": {
            transform: "scale(1)",
            boxShadow: "0 0 0 0 rgba(34, 197, 94, 0.4)",
          },
          "50%": {
            transform: "scale(1.05)",
            boxShadow: "0 0 0 10px rgba(34, 197, 94, 0)",
          },
          "100%": {
            transform: "scale(1)",
            boxShadow: "0 0 0 0 rgba(34, 197, 94, 0)",
          },
        },
      },
    },
  },
  plugins: [],
};
