import type { Metadata } from "next";
import { <PERSON>eist } from "next/font/google";
import { ThemeProvider } from "next-themes";
import "./globals.css";

const defaultUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : "http://localhost:3000";

export const metadata: Metadata = {
  title: "生财记 - 智能记账与财富管理工具 | WealthNote - Smart Expense Tracker & Personal Finance Manager",
  description: "生财记（WealthNote）是一款智能记账与财富管理工具，帮您轻松记录每一笔收支，实时统计资产、收入、支出与结余。支持多端同步、分类管理与财务报表，让理财更简单、更高效，助您实现财富增长。WealthNote is a smart expense tracker and personal finance manager that helps you easily record every transaction, track income and expenses, and monitor your assets in real time. Enjoy multi-device sync, category management, and detailed financial reports to make budgeting simple and boost your wealth.",
};

const geistSans = Geist({
  variable: "--font-geist-sans",
  display: "swap",
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${geistSans.className} antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
